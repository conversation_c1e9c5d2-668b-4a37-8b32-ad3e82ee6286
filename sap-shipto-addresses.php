<?php
/**
 * Plugin Name: SAP ShipTo Addresses API
 * Description: Processes SAP shipping addresses and stores them in WCMCA compatible format
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-shipto', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_shipto_addresses',
        'permission_callback' => 'sap_shipto_permission_check',
    ] );

    // Test endpoint (keep open for testing)
    register_rest_route( 'wc/v3', '/sap-shipto-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP ShipTo Addresses API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP ShipTo API
 * Uses JWT token authentication
 */
function sap_shipto_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP ShipTo API: Checking JWT authentication" );

    // Get the authorization header
    $auth_header = $request->get_header( 'authorization' );
    if ( ! $auth_header ) {
        // Also check for Authorization header (case variations)
        $auth_header = $request->get_header( 'Authorization' );
    }

    if ( ! $auth_header ) {
        error_log( "❌ SAP ShipTo API: No authorization header found" );
        return new WP_Error( 'jwt_auth_no_auth_header', 'Authorization header not found', [ 'status' => 401 ] );
    }

    // Extract the token from "Bearer <token>"
    if ( ! preg_match( '/Bearer\s+(.*)$/i', $auth_header, $matches ) ) {
        error_log( "❌ SAP ShipTo API: Invalid authorization header format" );
        return new WP_Error( 'jwt_auth_bad_auth_header', 'Authorization header malformed', [ 'status' => 401 ] );
    }

    $token = $matches[1];

    // Validate the JWT token using the existing JWT plugin
    $user = sap_validate_jwt_token( $token );

    if ( is_wp_error( $user ) ) {
        error_log( "❌ SAP ShipTo API: JWT validation failed - " . $user->get_error_message() );
        return $user;
    }

    if ( ! $user ) {
        error_log( "❌ SAP ShipTo API: JWT validation returned no user" );
        return new WP_Error( 'jwt_auth_invalid_token', 'Invalid token', [ 'status' => 401 ] );
    }

    // Check if user has appropriate capabilities
    if ( user_can( $user, 'manage_woocommerce' ) || user_can( $user, 'edit_shop_orders' ) || user_can( $user, 'manage_options' ) ) {
        error_log( "✅ SAP ShipTo API: JWT authentication successful for user " . $user->ID );
        return true;
    }

    error_log( "❌ SAP ShipTo API: User lacks sufficient permissions" );
    return new WP_Error( 'jwt_auth_insufficient_permissions', 'Insufficient permissions', [ 'status' => 403 ] );
}

/**
 * Validate JWT token using existing JWT plugin functionality
 */
function sap_validate_jwt_token( $token ) {
    // Check if JWT Auth plugin is available
    if ( ! class_exists( 'Jwt_Auth_Public' ) && ! function_exists( 'jwt_auth_validate_token' ) ) {
        error_log( "❌ SAP ShipTo API: JWT Auth plugin not found" );
        return new WP_Error( 'jwt_auth_not_available', 'JWT authentication not available', [ 'status' => 503 ] );
    }

    try {
        // Try to use the JWT Auth plugin's validation
        if ( class_exists( 'Jwt_Auth_Public' ) ) {
            $jwt_auth = new Jwt_Auth_Public( '', '' );
            $user = $jwt_auth->validate_token( $token );

            if ( is_wp_error( $user ) ) {
                return $user;
            }

            return $user;
        }

        // Alternative: if there's a global function
        if ( function_exists( 'jwt_auth_validate_token' ) ) {
            return jwt_auth_validate_token( $token );
        }

        // Fallback: try to decode the token manually if we have the secret
        if ( defined( 'JWT_AUTH_SECRET_KEY' ) ) {
            return sap_decode_jwt_token( $token );
        }

        return new WP_Error( 'jwt_auth_no_method', 'No JWT validation method available', [ 'status' => 503 ] );

    } catch ( Exception $e ) {
        error_log( "❌ SAP ShipTo API: JWT validation exception - " . $e->getMessage() );
        return new WP_Error( 'jwt_auth_exception', 'Token validation failed: ' . $e->getMessage(), [ 'status' => 401 ] );
    }
}

/**
 * Fallback JWT token decoder
 */
function sap_decode_jwt_token( $token ) {
    if ( ! defined( 'JWT_AUTH_SECRET_KEY' ) ) {
        return new WP_Error( 'jwt_auth_no_secret', 'JWT secret not configured', [ 'status' => 503 ] );
    }

    try {
        // This is a basic implementation - adjust based on your JWT library
        $parts = explode( '.', $token );
        if ( count( $parts ) !== 3 ) {
            return new WP_Error( 'jwt_auth_invalid_token', 'Invalid token format', [ 'status' => 401 ] );
        }

        // Decode the payload
        $payload = json_decode( base64_decode( $parts[1] ), true );

        if ( ! $payload || ! isset( $payload['data']['user']['id'] ) ) {
            return new WP_Error( 'jwt_auth_invalid_payload', 'Invalid token payload', [ 'status' => 401 ] );
        }

        $user_id = $payload['data']['user']['id'];
        $user = get_user_by( 'id', $user_id );

        if ( ! $user ) {
            return new WP_Error( 'jwt_auth_user_not_found', 'User not found', [ 'status' => 401 ] );
        }

        return $user;

    } catch ( Exception $e ) {
        return new WP_Error( 'jwt_auth_decode_error', 'Token decode error: ' . $e->getMessage(), [ 'status' => 401 ] );
    }
}

/**
 * Main endpoint to process SAP shipping addresses
 */
function sap_process_shipto_addresses( WP_REST_Request $request ) {
    error_log( "🚀 SAP ShipTo API called" );
    
    $data = $request->get_json_params();
    error_log( "🔍 Received data: " . print_r( $data, true ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) || empty( $data['shipTo'] ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected shipTo array.', [ 'status' => 400 ] );
    }

    $ship_to_addresses = $data['shipTo'];
    if ( ! is_array( $ship_to_addresses ) ) {
        error_log( "❌ shipTo is not an array" );
        return new WP_Error( 'invalid_shipto', 'shipTo must be an array', [ 'status' => 400 ] );
    }

    // Group addresses by customer ID
    $customers_addresses = [];
    foreach ( $ship_to_addresses as $address ) {
        if ( empty( $address['customerId'] ) ) {
            $errors[] = "Address missing customerId: " . json_encode( $address );
            continue;
        }

        $customer_id = sanitize_text_field( $address['customerId'] );
        if ( ! isset( $customers_addresses[$customer_id] ) ) {
            $customers_addresses[$customer_id] = [];
        }
        $customers_addresses[$customer_id][] = $address;
    }

    error_log( "🔍 Grouped addresses for " . count( $customers_addresses ) . " customers" );

    $processed_customers = [];
    $errors = [];

    // Process each customer's addresses (replace all existing addresses)
    foreach ( $customers_addresses as $customer_id => $addresses ) {
        error_log( "🔄 Processing " . count( $addresses ) . " addresses for customer: {$customer_id}" );

        $result = process_customer_shipto_addresses( $customer_id, $addresses );

        if ( is_wp_error( $result ) ) {
            $errors[] = $result->get_error_message();
            error_log( "❌ Error processing addresses for customer {$customer_id}: " . $result->get_error_message() );
        } else {
            $processed_customers[] = $result;
            error_log( "✅ Successfully replaced addresses for customer: " . $result['customer_id'] );
        }
    }

    // Return response
    $response = [
        'success' => true,
        'processed_customers' => $processed_customers,
        'total_processed' => count( $processed_customers ),
        'errors' => $errors
    ];

    if ( ! empty( $errors ) ) {
        $response['partial_success'] = true;
    }

    return rest_ensure_response( $response );
}

/**
 * Process shipping addresses for a single customer (replaces all existing addresses)
 */
function process_customer_shipto_addresses( $customer_id, $addresses ) {
    // Find user by customerId (_customer meta field)
    $user_id = find_user_by_customer_id( $customer_id );

    if ( ! $user_id ) {
        return new WP_Error( 'user_not_found', "User not found for customerId: {$customer_id}" );
    }

    error_log( "🔍 Found user ID {$user_id} for customer {$customer_id}" );
    error_log( "🔄 Replacing all addresses for customer {$customer_id} with " . count( $addresses ) . " new addresses" );

    $new_addresses = [];

    // Convert all SAP addresses to WCMCA format
    foreach ( $addresses as $address ) {
        $wcmca_address = convert_sap_to_wcmca_format( $address );

        if ( is_wp_error( $wcmca_address ) ) {
            return $wcmca_address;
        }

        $new_addresses[] = $wcmca_address;
    }

    // Handle default address logic - ensure only one default address
    $default_count = 0;
    foreach ( $new_addresses as $key => $address ) {
        if ( ! empty( $address['shipping_is_default_address'] ) && $address['shipping_is_default_address'] === '1' ) {
            $default_count++;
            // If this is not the first default address, remove the default flag
            if ( $default_count > 1 ) {
                unset( $new_addresses[$key]['shipping_is_default_address'] );
                error_log( "⚠️ Removed duplicate default flag from address: " . $address['address_internal_name'] );
            }
        }
    }

    // Replace all existing addresses with new ones
    $result = update_user_meta( $user_id, '_wcmca_additional_addresses', $new_addresses );

    if ( $result === false ) {
        return new WP_Error( 'meta_update_failed', "Failed to update addresses for user {$user_id}" );
    }

    error_log( "✅ Replaced all addresses for user {$user_id} with " . count( $new_addresses ) . " new addresses" );

    return [
        'user_id' => $user_id,
        'customer_id' => $customer_id,
        'addresses_replaced' => true,
        'total_addresses' => count( $new_addresses )
    ];
}

/**
 * Find WordPress user by customerId
 */
function find_user_by_customer_id( $customer_id ) {
    $users = get_users([
        'meta_key' => '_customer',
        'meta_value' => $customer_id,
        'number' => 1,
        'fields' => 'ID'
    ]);

    return ! empty( $users ) ? $users[0] : false;
}

/**
 * Convert SAP address format to WCMCA format
 */
function convert_sap_to_wcmca_format( $sap_address ) {
    // Generate unique address ID
    $address_id = ! empty( $sap_address['addressID'] ) ? $sap_address['addressID'] : uniqid();
    
    // Build WCMCA compatible address array
    $wcmca_address = [
        'type' => 'shipping',
        'address_id' => $address_id,
    ];

    // Map identifier to address_internal_name
    if ( ! empty( $sap_address['identifier'] ) ) {
        $wcmca_address['address_internal_name'] = sanitize_text_field( $sap_address['identifier'] );
    }

    // Map isDefaultAddress
    if ( isset( $sap_address['isDefaultAddress'] ) && $sap_address['isDefaultAddress'] === true ) {
        $wcmca_address['shipping_is_default_address'] = '1';
    }

    // Map company name
    if ( ! empty( $sap_address['companyName'] ) ) {
        $wcmca_address['shipping_company'] = sanitize_text_field( $sap_address['companyName'] );
    }

    // Map country
    if ( ! empty( $sap_address['country'] ) ) {
        $wcmca_address['shipping_country'] = sanitize_text_field( $sap_address['country'] );
    }

    // Map address fields
    if ( ! empty( $sap_address['address'] ) ) {
        $address = $sap_address['address'];
        
        // Combine street and house number for address_1
        $address_1_parts = [];
        if ( ! empty( $address['street'] ) ) {
            $address_1_parts[] = sanitize_text_field( $address['street'] );
        }
        if ( ! empty( $address['houseNumber'] ) ) {
            $address_1_parts[] = sanitize_text_field( $address['houseNumber'] );
        }
        if ( ! empty( $address_1_parts ) ) {
            $wcmca_address['shipping_address_1'] = implode( ' ', $address_1_parts );
        }

        // Map apartment to address_2
        if ( ! empty( $address['apartment'] ) ) {
            $wcmca_address['shipping_address_2'] = sanitize_text_field( $address['apartment'] );
        }

        // Map city
        if ( ! empty( $address['city'] ) ) {
            $wcmca_address['shipping_city'] = sanitize_text_field( $address['city'] );
        }

        // Map postal code
        if ( ! empty( $address['postalCode'] ) ) {
            $wcmca_address['shipping_postcode'] = sanitize_text_field( $address['postalCode'] );
        }
    }

    // Map stateCounty to shipping_state
    if ( ! empty( $sap_address['stateCounty'] ) ) {
        $wcmca_address['shipping_state'] = sanitize_text_field( $sap_address['stateCounty'] );
    }

    // Note: shipping_first_name and shipping_last_name are intentionally left empty
    // as per requirements (leave shipping_last_name empty)

    error_log( "🔍 Converted SAP address to WCMCA format: " . print_r( $wcmca_address, true ) );

    return $wcmca_address;
}

/**
 * Helper function to convert JSON to WordPress metadata format
 * Uses WordPress's built-in serialization for complex data
 */
function convert_json_to_metadata( $data ) {
    if ( is_array( $data ) || is_object( $data ) ) {
        return maybe_serialize( $data );
    }
    return $data;
}
