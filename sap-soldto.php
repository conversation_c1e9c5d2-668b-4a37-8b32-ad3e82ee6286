<?php
// DEBUG: catch any fatal errors and log them
add_action( 'init', function() {
    register_shutdown_function( function() {
        $err = error_get_last();
        if ( $err && in_array( $err['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR] ) ) {
            error_log( "🔥 Fatal error in SAP-SoldTo API: " . print_r( $err, true ) );
            error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        }
    });
});

// DEBUG: intercept wp_die() and log a trace
add_filter( 'wp_die_handler', function() {
    return function( $message, $title = '', $args = [] ) {
        error_log( "⚠️ wp_die fired: {$message}" );
        error_log( print_r( debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS ), true ) );
        // then call the normal handler so WP will still die
        _default_wp_die_handler( $message, $title, $args );
    };
});

/**
 * Plugin Name: SAP Sold TO API
 * Plugin URI: https://atakinteractive.com
 * Description: Creates WP users from JSON and adds all SoldTo/billing fields as user meta.
 * Version:     1.8
 * Author:      ATAK Interactive
 * Author URI: https://atakinteractive.com
 * Requires at least: 5.0
 * Tested up to: 6.7
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Debug: Log when plugin loads
error_log( "🔍 SAP-SoldTo plugin loaded at " . current_time( 'mysql' ) );

// Debug: Check if WooCommerce is active
add_action( 'init', function() {
    error_log( "🔍 SAP-SoldTo init hook - WooCommerce active: " . ( class_exists( 'WooCommerce' ) ? 'YES' : 'NO' ) );
} );

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-soldto', [
        'methods'             => 'POST',
        'callback'            => 'cuc_create_customer_endpoint',
        'permission_callback' => 'sap_soldto_permission_check',
    ] );

    // Add a simple GET endpoint for testing
    register_rest_route( 'wc/v3', '/sap-soldto-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            error_log( "🔍 SAP-SoldTo TEST endpoint called" );
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP SoldTo API is active',
                'timestamp' => current_time( 'mysql' ),
                'wordpress_version' => get_bloginfo( 'version' ),
                'woocommerce_active' => class_exists( 'WooCommerce' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true', // Keep test endpoint open
    ] );
} );

/**
 * Permission callback for SAP SoldTo API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_soldto_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP SoldTo API: Checking WordPress REST API authentication" );

    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();

    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP SoldTo API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }

    error_log( "🔍 SAP SoldTo API: Authenticated user: {$user->ID} ({$user->user_login})" );

    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP SoldTo API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];

    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP SoldTo API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP SoldTo API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}

/**
 * Permission callback with detailed logging
 */
function cuc_check_permissions( WP_REST_Request $request ) {
    error_log( "🔍 SAP-SoldTo permission check started" );

    // Get current user info
    $current_user = wp_get_current_user();
    error_log( "🔍 Current user ID: " . $current_user->ID );
    error_log( "🔍 Current user roles: " . print_r( $current_user->roles, true ) );

    // Check if user is logged in
    if ( ! is_user_logged_in() ) {
        error_log( "❌ User not logged in, checking for API authentication" );

        // Check for basic auth or other authentication methods
        $auth_header = $request->get_header( 'authorization' );
        if ( $auth_header ) {
            error_log( "🔍 Authorization header present: " . substr( $auth_header, 0, 20 ) . "..." );

            // Try to authenticate with basic auth
            if ( strpos( $auth_header, 'Basic ' ) === 0 ) {
                $credentials = base64_decode( substr( $auth_header, 6 ) );
                if ( $credentials && strpos( $credentials, ':' ) !== false ) {
                    list( $username, $password ) = explode( ':', $credentials, 2 );
                    $user = wp_authenticate( $username, $password );

                    if ( ! is_wp_error( $user ) ) {
                        wp_set_current_user( $user->ID );
                        error_log( "✅ Basic auth successful for user: " . $user->user_login );
                    } else {
                        error_log( "❌ Basic auth failed: " . $user->get_error_message() );
                    }
                }
            }
        }
    }

    // Re-check current user after potential authentication
    $current_user = wp_get_current_user();

    // Check capabilities
    $can_create_users = current_user_can( 'create_users' );
    $can_manage_wc = current_user_can( 'manage_woocommerce' );
    $is_admin = current_user_can( 'manage_options' );

    error_log( "🔍 Capability check - create_users: " . ( $can_create_users ? 'YES' : 'NO' ) );
    error_log( "🔍 Capability check - manage_woocommerce: " . ( $can_manage_wc ? 'YES' : 'NO' ) );
    error_log( "🔍 Capability check - manage_options: " . ( $is_admin ? 'YES' : 'NO' ) );

    // Allow if user has any of these capabilities
    if ( $can_create_users || $can_manage_wc || $is_admin ) {
        error_log( "✅ Permission granted" );
        return true;
    }

    // For development/testing - you can temporarily allow all requests
    // REMOVE THIS IN PRODUCTION!
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( "⚠️ DEBUG MODE: Allowing request without proper authentication" );
        return true;
    }

    error_log( "❌ Permission denied" );
    return false;
}

/**
 * Endpoint logic: Update/create database record and optionally update WordPress user
 */
function cuc_create_customer_endpoint( WP_REST_Request $request ) {
    // FIRST - Log that the endpoint was called
    error_log( "🚀 SAP-SoldTo API ENDPOINT CALLED!" );
    error_log( "🚀 Request method: " . $request->get_method() );
    error_log( "🚀 Request URL: " . $request->get_route() );

    $data = $request->get_json_params();

    // Log the incoming data for debugging
    error_log( "🔍 SAP-SoldTo API received data: " . print_r( $data, true ) );

    // — Enhanced Validation —
    if ( empty( $data ) || ! is_array( $data ) ) {
        error_log( "❌ No valid JSON data received" );
        return new WP_Error( 'invalid_data', 'No valid JSON data received', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo'] ) || ! is_array( $data['soldTo'] ) ) {
        error_log( "❌ soldTo data is missing or invalid" );
        return new WP_Error( 'missing_soldto', 'soldTo data is required and must be an object', [ 'status'=>400 ] );
    }

    if ( empty( $data['soldTo']['customerId'] ) ) {
        error_log( "❌ customerId is missing" );
        return new WP_Error( 'missing_customer_id', 'soldTo.customerId is required', [ 'status'=>400 ] );
    }

    // Email is not required for database-only updates
    $email = isset( $data['soldTo']['email'] ) ? sanitize_email( $data['soldTo']['email'] ) : '';
    if ( ! empty( $email ) && ! is_email( $email ) ) {
        error_log( "❌ Invalid email format: " . $email );
        return new WP_Error( 'invalid_email', 'Invalid email format', [ 'status'=>400 ] );
    }

    $customer_id = sanitize_text_field( $data['soldTo']['customerId'] );

    // Step 1: Update/Create database record
    $db_result = sap_update_customer_database( $data, $customer_id );
    if ( is_wp_error( $db_result ) ) {
        return $db_result;
    }

    // Step 2: Find existing WordPress user by customer_id
    $wp_user_id = sap_find_user_by_customer_id( $customer_id );

    $response = [
        'success' => true,
        'customer_id' => $customer_id,
        'database_updated' => true,
        'database_record_id' => $db_result['record_id'],
        'wp_user_found' => false,
        'wp_user_updated' => false
    ];

    // Step 3: Update WordPress user metadata if user exists
    if ( $wp_user_id ) {
        error_log( "🔍 Found existing WordPress user {$wp_user_id} for customer {$customer_id}" );
        $user_update_result = sap_update_user_metadata( $wp_user_id, $data );

        if ( is_wp_error( $user_update_result ) ) {
            $response['wp_user_error'] = $user_update_result->get_error_message();
        } else {
            $response['wp_user_found'] = true;
            $response['wp_user_updated'] = true;
            $response['wp_user_id'] = $wp_user_id;
        }

        // Update the database record with the WordPress user ID
        sap_link_database_record_to_user( $customer_id, $wp_user_id );
    } else {
        error_log( "🔍 No existing WordPress user found for customer {$customer_id}" );
        $response['message'] = 'Database record updated. No WordPress user found for this customer.';
    }

    return rest_ensure_response( $response );
}

/**
 * Update or create customer record in the database
 */
function sap_update_customer_database( $data, $customer_id ) {
    global $wpdb;

    error_log( "🔍 Updating database record for customer: {$customer_id}" );

    $sold_to = $data['soldTo'];
    $billing = isset( $data['billingAddress'] ) ? $data['billingAddress'] : [];
    $address = isset( $billing['address'] ) ? $billing['address'] : [];
    $shiptos = isset( $data['shiptos'] ) ? $data['shiptos'] : [];

    // Prepare data for database
    $db_data = [
        'customer_id' => $customer_id,
        'company_code' => sanitize_text_field( $sold_to['companyCode'] ?? '' ),
        'country_code' => sanitize_text_field( $sold_to['countryCode'] ?? '' ),
        'price_group' => sanitize_text_field( $sold_to['priceGroup'] ?? '' ),
        'email' => sanitize_email( $sold_to['email'] ?? '' ),
        'company' => sanitize_text_field( $billing['company'] ?? '' ),
        'address_line1' => sanitize_text_field( $address['line1'] ?? '' ),
        'address_line2' => sanitize_text_field( $address['line2'] ?? '' ),
        'city' => sanitize_text_field( $address['city'] ?? '' ),
        'postcode' => sanitize_text_field( $address['postcode'] ?? '' ),
        'country_region' => sanitize_text_field( $address['countryRegion'] ?? '' ),
        'state_county' => sanitize_text_field( $address['stateCounty'] ?? '' ),
        'shiptos' => wp_json_encode( $shiptos ), // Store as JSON
        'updated_at' => current_time( 'mysql' )
    ];

    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    // Check if record exists
    $existing_record = $wpdb->get_row( $wpdb->prepare(
        "SELECT id, wp_user_id FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ) );

    if ( $existing_record ) {
        // Update existing record
        error_log( "🔄 Updating existing database record for customer {$customer_id}" );

        // Don't overwrite wp_user_id if it already exists
        if ( $existing_record->wp_user_id ) {
            unset( $db_data['wp_user_id'] );
        }

        $result = $wpdb->update(
            $table_name,
            $db_data,
            [ 'customer_id' => $customer_id ],
            [
                '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
                '%s', '%s', '%s', '%s', '%s', '%s'
            ],
            [ '%s' ]
        );

        if ( $result === false ) {
            error_log( "❌ Database update failed: " . $wpdb->last_error );
            return new WP_Error( 'db_update_failed', 'Failed to update database record', [ 'status' => 500 ] );
        }

        error_log( "✅ Database record updated for customer {$customer_id}" );
        return [ 'record_id' => $existing_record->id, 'action' => 'updated' ];

    } else {
        // Insert new record
        error_log( "➕ Creating new database record for customer {$customer_id}" );

        $db_data['created_at'] = current_time( 'mysql' );

        $result = $wpdb->insert(
            $table_name,
            $db_data,
            [
                '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s',
                '%s', '%s', '%s', '%s', '%s', '%s', '%s'
            ]
        );

        if ( $result === false ) {
            error_log( "❌ Database insert failed: " . $wpdb->last_error );
            return new WP_Error( 'db_insert_failed', 'Failed to create database record', [ 'status' => 500 ] );
        }

        $record_id = $wpdb->insert_id;
        error_log( "✅ Database record created for customer {$customer_id} with ID {$record_id}" );
        return [ 'record_id' => $record_id, 'action' => 'created' ];
    }
}

/**
 * Find WordPress user by customer ID
 */
function sap_find_user_by_customer_id( $customer_id ) {
    $users = get_users([
        'meta_key' => '_customer',
        'meta_value' => $customer_id,
        'number' => 1,
        'fields' => 'ID'
    ]);

    return ! empty( $users ) ? $users[0] : false;
}

/**
 * Update WordPress user metadata
 */
function sap_update_user_metadata( $user_id, $data ) {
    error_log( "🔍 Updating WordPress user metadata for user {$user_id}" );

    try {
        $sold_to = $data['soldTo'];
        $billing = isset( $data['billingAddress'] ) ? $data['billingAddress'] : [];
        $address = isset( $billing['address'] ) ? $billing['address'] : [];
        $shiptos = isset( $data['shiptos'] ) ? $data['shiptos'] : [];

        // Update SoldTo metadata
        $soldto_fields = [
            '_customer' => $sold_to['customerId'] ?? '',
            '_companycode' => $sold_to['companyCode'] ?? '',
            '_country' => $sold_to['countryCode'] ?? '',
            '_pricegroup' => $sold_to['priceGroup'] ?? ''
        ];

        foreach ( $soldto_fields as $meta_key => $meta_value ) {
            if ( ! empty( $meta_value ) ) {
                $sanitized_value = sanitize_text_field( $meta_value );
                update_user_meta( $user_id, $meta_key, $sanitized_value );
                error_log( "✅ Updated {$meta_key}: {$sanitized_value}" );
            }
        }

        // Update billing metadata
        if ( ! empty( $billing['company'] ) ) {
            update_user_meta( $user_id, 'billing_company', sanitize_text_field( $billing['company'] ) );
        }

        $billing_fields = [
            'billing_address_1' => $address['line1'] ?? '',
            'billing_address_2' => $address['line2'] ?? '',
            'billing_city' => $address['city'] ?? '',
            'billing_postcode' => $address['postcode'] ?? '',
            'billing_country' => $address['countryRegion'] ?? '',
            'billing_state' => $address['stateCounty'] ?? '',
        ];

        foreach ( $billing_fields as $meta_key => $meta_value ) {
            if ( ! empty( $meta_value ) ) {
                update_user_meta( $user_id, $meta_key, sanitize_text_field( $meta_value ) );
                error_log( "✅ Updated {$meta_key}: {$meta_value}" );
            }
        }

        // Update shiptos
        if ( ! empty( $shiptos ) ) {
            $sanitized_shiptos = array_map( 'sanitize_text_field', $shiptos );
            update_user_meta( $user_id, '_shiptos', $sanitized_shiptos );
            error_log( "✅ Updated _shiptos: " . print_r( $sanitized_shiptos, true ) );
        }

        error_log( "✅ All user metadata updated successfully for user {$user_id}" );
        return true;

    } catch ( Exception $e ) {
        error_log( "❌ Error updating user metadata: " . $e->getMessage() );
        return new WP_Error( 'metadata_update_failed', 'Failed to update user metadata: ' . $e->getMessage() );
    }
}

/**
 * Link database record to WordPress user
 */
function sap_link_database_record_to_user( $customer_id, $user_id ) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    $result = $wpdb->update(
        $table_name,
        [ 'wp_user_id' => $user_id ],
        [ 'customer_id' => $customer_id ],
        [ '%d' ],
        [ '%s' ]
    );

    if ( $result !== false ) {
        error_log( "✅ Linked database record for customer {$customer_id} to user {$user_id}" );
    } else {
        error_log( "❌ Failed to link database record: " . $wpdb->last_error );
    }
}
