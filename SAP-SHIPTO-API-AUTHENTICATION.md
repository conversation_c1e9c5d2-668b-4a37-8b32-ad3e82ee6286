# SAP ShipTo API Authentication Setup

The SAP ShipTo Addresses API now requires proper WooCommerce REST API authentication for security.

## 🔐 Authentication Methods

The API supports two authentication methods:

### 1. **WooCommerce API Keys (Recommended)**
- Most secure for external systems
- Easy to manage and revoke
- Supports different permission levels

### 2. **WordPress User Authentication**
- For internal/admin use
- Requires user with appropriate capabilities

## 🚀 Setting Up WooCommerce API Keys

### Step 1: Create API Keys in WordPress Admin

1. **Go to WooCommerce Settings**
   - Navigate to: `WooCommerce → Settings → Advanced → REST API`

2. **Add New API Key**
   - Click "Add Key"
   - Fill in the details:
     - **Description**: `SAP ShipTo Integration`
     - **User**: Select an admin user
     - **Permissions**: `Read/Write` (required for the API)

3. **Save and Copy Credentials**
   - Click "Generate API Key"
   - **IMPORTANT**: Copy the `Consumer Key` and `Consumer Secret` immediately
   - Store them securely - you won't see the secret again!

### Step 2: Test the API Key

```bash
# Test the API key with the test endpoint
curl -X GET \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto-test' \
  -u 'ck_your_consumer_key:cs_your_consumer_secret'
```

## 📡 Using the Authenticated API

### Basic Authentication (Recommended)

```bash
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto' \
  -u 'ck_your_consumer_key:cs_your_consumer_secret' \
  -H 'Content-Type: application/json' \
  -d '{
    "shipTo": [
      {
        "customerId": "C123456",
        "identifier": "Main Warehouse",
        "isDefaultAddress": true,
        "country": "US",
        "address": {
          "street": "123 Main St",
          "city": "Springfield",
          "postalCode": "12345"
        }
      }
    ]
  }'
```

### Query Parameter Authentication (Alternative)

```bash
curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-shipto?consumer_key=ck_your_key&consumer_secret=cs_your_secret' \
  -H 'Content-Type: application/json' \
  -d '{"shipTo": [...]}'
```

## 🔧 PowerShell Example with Authentication

```powershell
# Set your API credentials
$ConsumerKey = "ck_your_consumer_key_here"
$ConsumerSecret = "cs_your_consumer_secret_here"
$SiteUrl = "https://yoursite.com"

# Create authentication header
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${ConsumerKey}:${ConsumerSecret}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

# Your shipping data
$shipToData = @{
    shipTo = @(
        @{
            customerId = "C123456"
            identifier = "Main Warehouse"
            isDefaultAddress = $true
            country = "US"
            address = @{
                street = "123 Main St"
                city = "Springfield"
                postalCode = "12345"
            }
        }
    )
} | ConvertTo-Json -Depth 10

# Send the request
try {
    $response = Invoke-RestMethod -Uri "$SiteUrl/wp-json/wc/v3/sap-shipto" -Method Post -Headers $headers -Body $shipToData
    Write-Host "✅ Success!" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
```

## 🛡️ Security Best Practices

### 1. **API Key Management**
- Use descriptive names for API keys
- Regularly rotate API keys
- Delete unused API keys
- Monitor API key usage

### 2. **Permissions**
- Only grant `Read/Write` permissions (not `Read/Write/Delete`)
- Create dedicated API keys for each integration
- Don't share API keys between systems

### 3. **Network Security**
- Always use HTTPS in production
- Consider IP whitelisting if possible
- Monitor API access logs

### 4. **Error Handling**
- Don't expose API keys in error messages
- Log authentication failures for monitoring
- Implement rate limiting if needed

## 🔍 Troubleshooting Authentication

### Common Issues:

1. **401 Unauthorized**
   - Check consumer key/secret are correct
   - Verify the API key exists and is active
   - Ensure you're using the right authentication method

2. **403 Forbidden**
   - API key doesn't have sufficient permissions
   - User associated with API key lacks capabilities
   - Check the API key permissions are `Read/Write`

3. **404 Not Found**
   - Plugin not activated
   - Wrong endpoint URL
   - WooCommerce not active

### Debug Steps:

1. **Test the test endpoint first**:
   ```bash
   curl -u 'key:secret' 'https://yoursite.com/wp-json/wc/v3/sap-shipto-test'
   ```

2. **Check WordPress error logs** for authentication messages

3. **Verify WooCommerce API keys** in admin panel

4. **Test with a simple WooCommerce endpoint**:
   ```bash
   curl -u 'key:secret' 'https://yoursite.com/wp-json/wc/v3/products'
   ```

## 📝 API Response Codes

- **200**: Success - addresses processed
- **400**: Bad request - invalid data format
- **401**: Unauthorized - authentication failed
- **403**: Forbidden - insufficient permissions
- **404**: Not found - endpoint not available
- **500**: Server error - check logs

## 🔄 Migration from Test Version

If you were using the test version without authentication:

1. **Create API keys** as described above
2. **Update your integration** to include authentication
3. **Test thoroughly** before going live
4. **Remove any test/debug plugins** that bypass authentication

The API functionality remains exactly the same - only authentication is now required for security.
