# PowerShell script to test SAP ShipTo Addresses API
# Usage: .\test-sap-shipto.ps1

Write-Host "🚀 Testing SAP ShipTo Addresses API" -ForegroundColor Green
Write-Host ""

# Configuration - Update these values for your environment
$SiteUrl = "http://localhost/your-wordpress-site"  # Update this URL
$ConsumerKey = "ck_your_consumer_key_here"         # Add your WooCommerce API key
$ConsumerSecret = "cs_your_consumer_secret_here"   # Add your WooCommerce API secret

$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-shipto"
$TestEndpoint = "$SiteUrl/wp-json/wc/v3/sap-shipto-test"

# Create authentication header
$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${ConsumerKey}:${ConsumerSecret}"))
$headers = @{
    "Authorization" = "Basic $auth"
    "Content-Type" = "application/json"
}

Write-Host "Site URL: $SiteUrl" -ForegroundColor Yellow
Write-Host "API Endpoint: $ApiEndpoint" -ForegroundColor Yellow
Write-Host "Authentication: Using WooCommerce API Keys" -ForegroundColor Yellow
Write-Host ""

# Check if credentials are set
if ($ConsumerKey -eq "ck_your_consumer_key_here" -or $ConsumerSecret -eq "cs_your_consumer_secret_here") {
    Write-Host "⚠️  WARNING: Please update the ConsumerKey and ConsumerSecret variables with your actual WooCommerce API credentials!" -ForegroundColor Red
    Write-Host "   See SAP-SHIPTO-API-AUTHENTICATION.md for setup instructions" -ForegroundColor Yellow
    Write-Host ""
}

# Test 1: Check if the API is active
Write-Host "📡 Test 1: Checking API Status..." -ForegroundColor Cyan

try {
    # Test endpoint doesn't require auth, but let's test it anyway
    $testResponse = Invoke-RestMethod -Uri $TestEndpoint -Method Get -ContentType "application/json"
    Write-Host "✅ API Status: " -ForegroundColor Green -NoNewline
    Write-Host $testResponse.message -ForegroundColor White
    Write-Host "   Timestamp: $($testResponse.timestamp)" -ForegroundColor Gray
    Write-Host "   Auth Required: $($testResponse.auth_required)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure WordPress is running and the plugin is activated." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Send sample shipping addresses (REPLACE MODE)
Write-Host "📦 Test 2: Sending Sample Shipping Addresses (REPLACE MODE)..." -ForegroundColor Cyan
Write-Host "   Note: This will REPLACE all existing addresses for the customer" -ForegroundColor Yellow

$testData = @{
    shipTo = @(
        @{
            addressID = 1128545
            customerId = "C123456"
            companyCode = "1000"
            identifier = "Main Warehouse"
            isDefaultAddress = $true
            companyName = $null
            country = "US"
            address = @{
                street = "Elm Street"
                apartment = $null
                city = "Springwood"
                postalCode = "12345"
            }
            stateCounty = "NY"
        },
        @{
            customerId = "C123456"
            companyCode = "1000"
            identifier = "Secondary Warehouse"
            isDefaultAddress = $false
            companyName = $null
            country = "US"
            address = @{
                street = "Pine Road"
                houseNumber = "99B"
                apartment = "Unit 5"
                city = "Springwood"
                postalCode = "12346"
            }
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending data:" -ForegroundColor Yellow
Write-Host $testData -ForegroundColor Gray
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers
    
    Write-Host "✅ SUCCESS: API processed the request!" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Response Details:" -ForegroundColor Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor White
    Write-Host "  Total Processed: $($response.total_processed)" -ForegroundColor White
    
    if ($response.processed_customers) {
        Write-Host "  Processed Customers:" -ForegroundColor White
        foreach ($customer in $response.processed_customers) {
            Write-Host "    - Customer ID: $($customer.customer_id), User ID: $($customer.user_id), Addresses: $($customer.total_addresses)" -ForegroundColor Gray
        }
    }
    
    if ($response.errors -and $response.errors.Count -gt 0) {
        Write-Host "  Errors:" -ForegroundColor Red
        foreach ($error in $response.errors) {
            Write-Host "    - $error" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Full Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor Gray
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $errorBody = ""
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        $reader.Close()
        $errorStream.Close()
    } catch {
        $errorBody = "Could not read error response"
    }
    
    Write-Host "❌ ERROR: HTTP Status $statusCode" -ForegroundColor Red
    Write-Host "Error Details: $errorBody" -ForegroundColor Red
    
    if ($statusCode -eq 404) {
        Write-Host "   The endpoint was not found. Make sure the plugin is activated." -ForegroundColor Yellow
    } elseif ($statusCode -eq 500) {
        Write-Host "   Server error. Check WordPress error logs for details." -ForegroundColor Yellow
    } elseif ($statusCode -eq 400) {
        Write-Host "   Bad request. Check the data format." -ForegroundColor Yellow
    }
}

Write-Host ""

# Test 3: Test replacement behavior
Write-Host "🔄 Test 3: Testing Address Replacement..." -ForegroundColor Cyan
Write-Host "   Sending a different set of addresses to verify replacement" -ForegroundColor Yellow

$replacementData = @{
    shipTo = @(
        @{
            addressID = 9999999
            customerId = "C123456"
            companyCode = "1000"
            identifier = "Replacement Warehouse"
            isDefaultAddress = $true
            companyName = "New Company Name"
            country = "CA"
            address = @{
                street = "Replacement Street"
                houseNumber = "123"
                city = "Toronto"
                postalCode = "M5V 3A8"
            }
            stateCounty = "ON"
        }
    )
} | ConvertTo-Json -Depth 10

try {
    $replaceResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $replacementData -ContentType "application/json"

    Write-Host "✅ Replacement test successful!" -ForegroundColor Green
    Write-Host "   The customer should now have only 1 address (the replacement one)" -ForegroundColor White
    Write-Host "   Total addresses: $($replaceResponse.processed_customers[0].total_addresses)" -ForegroundColor White

} catch {
    Write-Host "❌ Replacement test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Test with invalid data
Write-Host "🔍 Test 4: Testing Error Handling..." -ForegroundColor Cyan

$invalidData = @{
    shipTo = @(
        @{
            # Missing customerId - should cause an error
            identifier = "Invalid Address"
            country = "US"
        }
    )
} | ConvertTo-Json -Depth 5

try {
    $errorResponse = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $invalidData -ContentType "application/json"
    
    if ($errorResponse.errors -and $errorResponse.errors.Count -gt 0) {
        Write-Host "✅ Error handling works correctly:" -ForegroundColor Green
        foreach ($error in $errorResponse.errors) {
            Write-Host "   - $error" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  Expected errors but got success response" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✅ Error handling works - API returned error as expected" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Check WordPress error logs for detailed processing information" -ForegroundColor White
Write-Host "2. Verify that addresses were REPLACED (not added) in the user's metadata" -ForegroundColor White
Write-Host "3. Test with real customer IDs from your database" -ForegroundColor White
Write-Host "4. Update the site URL in this script for your environment" -ForegroundColor White
Write-Host "5. Remember: Each API call replaces ALL addresses for each customer" -ForegroundColor Cyan
Write-Host ""
Write-Host "To check user metadata, you can run the test-sap-shipto.php script in your browser" -ForegroundColor Cyan
