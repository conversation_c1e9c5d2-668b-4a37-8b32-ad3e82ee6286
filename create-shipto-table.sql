-- SQL to create SAP ShipTo Addresses table
-- This table stores shipping addresses in WCMCA compatible format for reuse

CREATE TABLE wp_sap_shipto_addresses (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    customer_id varchar(50) NOT NULL,
    address_id varchar(100) NOT NULL,
    address_internal_name varchar(255) DEFAULT '',
    type varchar(20) DEFAULT 'shipping',
    
    -- Default address flag
    shipping_is_default_address varchar(3) DEFAULT '',
    
    -- Company information
    shipping_company varchar(255) DEFAULT '',
    
    -- Name fields (intentionally left empty as per requirements)
    shipping_first_name varchar(100) DEFAULT '',
    shipping_last_name varchar(100) DEFAULT '',
    
    -- Address fields
    shipping_address_1 varchar(255) DEFAULT '',
    shipping_address_2 varchar(255) DEFAULT '',
    shipping_city varchar(100) DEFAULT '',
    shipping_postcode varchar(20) DEFAULT '',
    shipping_country varchar(5) DEFAULT '',
    shipping_state varchar(100) DEFAULT '',
    
    -- Original SAP data for reference
    sap_address_id varchar(100) DEFAULT '',
    sap_identifier varchar(255) DEFAULT '',
    sap_company_name varchar(255) DEFAULT '',
    sap_country varchar(5) DEFAULT '',
    sap_street varchar(255) DEFAULT '',
    sap_house_number varchar(50) DEFAULT '',
    sap_apartment varchar(100) DEFAULT '',
    sap_city varchar(100) DEFAULT '',
    sap_postal_code varchar(20) DEFAULT '',
    sap_state_county varchar(100) DEFAULT '',
    sap_is_default_address tinyint(1) DEFAULT 0,
    
    -- WordPress integration
    wp_user_id bigint(20) DEFAULT NULL,
    
    -- Timestamps
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Status
    status varchar(20) DEFAULT 'active',
    
    -- Indexes
    PRIMARY KEY (id),
    UNIQUE KEY unique_customer_address (customer_id, address_id),
    KEY customer_id (customer_id),
    KEY wp_user_id (wp_user_id),
    KEY address_id (address_id),
    KEY shipping_is_default (shipping_is_default),
    KEY status (status),
    KEY created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for performance
CREATE INDEX idx_customer_default ON wp_sap_shipto_addresses (customer_id, shipping_is_default_address);
CREATE INDEX idx_customer_status ON wp_sap_shipto_addresses (customer_id, status);
CREATE INDEX idx_sap_address_id ON wp_sap_shipto_addresses (sap_address_id);

-- Comments for documentation
ALTER TABLE wp_sap_shipto_addresses 
COMMENT = 'SAP ShipTo addresses stored in WCMCA compatible format for dual storage with WordPress metadata';

-- Sample data structure comment
/*
This table stores shipping addresses in a format compatible with WCMCA plugin:
- Each row represents one shipping address for a customer
- customer_id can have multiple addresses (one-to-many relationship)
- address_id should be unique per customer (customer_id + address_id = unique)
- WCMCA fields (shipping_*) are ready for direct use with _wcmca_additional_addresses
- SAP fields (sap_*) preserve original data for reference
- wp_user_id links to WordPress users when available
*/
